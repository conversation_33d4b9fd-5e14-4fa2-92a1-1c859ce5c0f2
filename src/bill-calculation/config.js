const fs = require("fs");
const path = require("path");

// 获取项目根目录
const projectRoot = path.resolve(__dirname, "../..");

// 定义目录路径
const inputDir = path.join(projectRoot, "input", "bill-calculation");
const jsonBaseDir = path.join(projectRoot, "json", "bill-calculation");
const outputDir = path.join(projectRoot, "output", "bill-calculation");

/**
 * 确保目录存在
 */
function ensureDirectories() {
  const directories = [inputDir, jsonBaseDir, outputDir];
  
  directories.forEach((dir) => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      console.log(`\x1b[36m[创建] \x1b[0m目录: \x1b[33m${dir}\x1b[0m`);
    }
  });
}

/**
 * 显示欢迎信息
 */
function showWelcomeMessage() {
  console.log(
    "\x1b[36m==================================================\x1b[0m"
  );
  console.log("\x1b[32m           清单测算 - 功能1\x1b[0m");
  console.log("\x1b[32m      读取分部分项成本测算数据\x1b[0m");
  console.log(
    "\x1b[36m==================================================\x1b[0m"
  );
}

/**
 * 配置控制台输出
 */
function configureConsole() {
  // 设置控制台编码为UTF-8
  if (process.platform === "win32") {
    process.stdout.setEncoding("utf8");
  }
}

module.exports = {
  inputDir,
  jsonBaseDir,
  outputDir,
  ensureDirectories,
  showWelcomeMessage,
  configureConsole,
};
