const fs = require("fs");
const path = require("path");

/**
 * 将处理后的数据写入JSON文件
 * @param {Array} processedData - 处理后的数据
 * @param {string} outputDir - 输出目录
 * @param {string} fileName - 文件名（不含扩展名）
 * @returns {string} 生成的JSON文件路径
 */
function writeJsonFile(processedData, outputDir, fileName) {
  console.log(`\n\x1b[36m[写入] \x1b[0m开始写入JSON文件...`);
  
  // 确保输出目录存在
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
    console.log(`\x1b[36m[创建] \x1b[0m目录: \x1b[33m${outputDir}\x1b[0m`);
  }
  
  // 生成文件路径
  const jsonFilePath = path.join(outputDir, `${fileName}.json`);
  
  try {
    // 写入JSON文件，保持数组顺序
    const jsonContent = JSON.stringify(processedData, null, 2);
    fs.writeFileSync(jsonFilePath, jsonContent, "utf8");
    
    console.log(`\x1b[32m[成功] \x1b[0m JSON文件已保存: \x1b[33m${jsonFilePath}\x1b[0m`);
    console.log(`\x1b[36m[信息] \x1b[0m文件大小: \x1b[32m${(jsonContent.length / 1024).toFixed(2)} KB\x1b[0m`);
    console.log(`\x1b[36m[信息] \x1b[0m数据条数: \x1b[32m${processedData.length}\x1b[0m`);
    
    return jsonFilePath;
    
  } catch (error) {
    console.error(`\x1b[31m[错误] \x1b[0m写入JSON文件失败: ${error.message}`);
    throw error;
  }
}

/**
 * 生成数据摘要信息
 * @param {Array} processedData - 处理后的数据
 * @returns {Object} 数据摘要
 */
function generateDataSummary(processedData) {
  const summary = {
    总数据条数: processedData.length,
    分部数量: 0,
    目标数量: 0,
    人材专数量: 0,
    分部列表: [],
    生成时间: new Date().toISOString(),
  };
  
  const 分部Set = new Set();
  
  processedData.forEach(item => {
    switch (item.数据级别) {
      case 1:
        summary.分部数量++;
        分部Set.add(item.目标);
        break;
      case 2:
        summary.目标数量++;
        break;
      case 3:
        summary.人材专数量++;
        break;
    }
  });
  
  summary.分部列表 = Array.from(分部Set);
  
  return summary;
}

/**
 * 写入数据摘要文件
 * @param {Array} processedData - 处理后的数据
 * @param {string} outputDir - 输出目录
 * @param {string} fileName - 文件名（不含扩展名）
 * @returns {string} 生成的摘要文件路径
 */
function writeSummaryFile(processedData, outputDir, fileName) {
  console.log(`\n\x1b[36m[摘要] \x1b[0m生成数据摘要...`);
  
  const summary = generateDataSummary(processedData);
  const summaryFilePath = path.join(outputDir, `${fileName}_summary.json`);
  
  try {
    const summaryContent = JSON.stringify(summary, null, 2);
    fs.writeFileSync(summaryFilePath, summaryContent, "utf8");
    
    console.log(`\x1b[32m[成功] \x1b[0m摘要文件已保存: \x1b[33m${summaryFilePath}\x1b[0m`);
    
    // 显示摘要信息
    console.log(`\n\x1b[36m[摘要信息] \x1b[0m`);
    console.log(`  \x1b[32m总数据条数: ${summary.总数据条数}\x1b[0m`);
    console.log(`  \x1b[32m分部数量: ${summary.分部数量}\x1b[0m`);
    console.log(`  \x1b[32m目标数量: ${summary.目标数量}\x1b[0m`);
    console.log(`  \x1b[32m人/材/专数量: ${summary.人材专数量}\x1b[0m`);
    console.log(`  \x1b[32m分部列表: ${summary.分部列表.join(', ')}\x1b[0m`);
    
    return summaryFilePath;
    
  } catch (error) {
    console.error(`\x1b[31m[错误] \x1b[0m写入摘要文件失败: ${error.message}`);
    throw error;
  }
}

/**
 * 批量写入JSON文件和摘要文件
 * @param {Array} processedData - 处理后的数据
 * @param {string} outputDir - 输出目录
 * @param {string} fileName - 文件名（不含扩展名）
 * @returns {Object} 包含文件路径的对象
 */
function writeAllFiles(processedData, outputDir, fileName) {
  const jsonFilePath = writeJsonFile(processedData, outputDir, fileName);
  const summaryFilePath = writeSummaryFile(processedData, outputDir, fileName);
  
  return {
    jsonFilePath,
    summaryFilePath,
  };
}

module.exports = {
  writeJsonFile,
  writeSummaryFile,
  writeAllFiles,
  generateDataSummary,
};
