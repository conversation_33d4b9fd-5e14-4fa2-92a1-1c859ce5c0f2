/**
 * 根据目标文本判断材料类型
 * @param {string} 目标 - 目标文本
 * @returns {string} 材料类型标记
 */
function getMaterialType(目标) {
  if (!目标) return "";

  const 目标文本 = 目标.toString();

  // 按优先级顺序检查
  if (目标文本.includes("钢筋")) {
    return "钢筋";
  } else if (目标文本.includes("商品混凝土C")) {
    return "商品混凝土";
  } else if (目标文本.includes("砂浆")) {
    return "砂浆";
  } else if (目标文本.includes("砖")) {
    return "砖";
  }

  return "";
}

/**
 * 处理原始数据，生成平级结构的数据
 * @param {Array} rawData - 原始数据数组
 * @returns {Array} 处理后的平级结构数据
 */
function processRawData(rawData) {
  console.log(`\n\x1b[36m[处理] \x1b[0m开始处理数据，生成平级结构...`);

  const processedData = [];
  let current分部 = "";
  let current目标 = "";

  for (let i = 0; i < rawData.length; i++) {
    const item = rawData[i];

    // 跳过合计行
    if (item.分部 === "合计行" || item.目标 === "合计行") {
      continue;
    }

    // 判断数据类型
    if (item.分部 === "分部") {
      // 1级数据：分部
      current分部 = item.目标;
      current目标 = ""; // 重置目标

      const processedItem = {
        序号: item.序号,
        分部: item.分部,
        目标: item.目标,
        项目特征: item.项目特征,
        计量单位: item.计量单位,
        工程量: item.工程量,
        含量: item.含量,
        原始行号: item.原始行号,
        数据级别: 1,
        父级分部: "",
        父级目标: "",
      };

      processedData.push(processedItem);

      console.log(`  \x1b[36m[1级] \x1b[0m分部: \x1b[33m${current分部}\x1b[0m`);
    } else if (item.分部 === "目标") {
      // 2级数据：目标
      current目标 = item.目标;

      const processedItem = {
        序号: item.序号,
        分部: item.分部,
        目标: item.目标,
        项目特征: item.项目特征,
        计量单位: item.计量单位,
        工程量: item.工程量,
        含量: item.含量,
        原始行号: item.原始行号,
        数据级别: 2,
        父级分部: current分部,
        父级目标: "",
      };

      processedData.push(processedItem);

      console.log(
        `    \x1b[36m[2级] \x1b[0m目标: \x1b[32m${current目标}\x1b[0m (分部: ${current分部})`
      );
    } else if (["人", "材", "专"].includes(item.分部)) {
      // 3级数据：人/材/专
      const 材料类型 = getMaterialType(item.目标);

      const processedItem = {
        序号: item.序号,
        分部: item.分部,
        目标: item.目标,
        项目特征: item.项目特征,
        计量单位: item.计量单位,
        工程量: item.工程量,
        含量: item.含量,
        原始行号: item.原始行号,
        数据级别: 3,
        父级分部: current分部,
        父级目标: current目标,
        材料类型: 材料类型,
      };

      processedData.push(processedItem);

      // 每处理10个3级数据显示一次进度
      const level3Count = processedData.filter((d) => d.数据级别 === 3).length;
      if (level3Count % 10 === 0) {
        console.log(
          `      \x1b[36m[3级] \x1b[0m已处理 \x1b[32m${level3Count}\x1b[0m 个人/材/专项目`
        );
      }

      // 显示材料类型标记（仅当有标记时，且前5个显示详细信息）
      if (材料类型 && level3Count <= 5) {
        console.log(
          `        \x1b[35m[标记] \x1b[0m${item.目标} -> \x1b[33m${材料类型}\x1b[0m`
        );
      }
    }
  }

  console.log(`\n\x1b[36m[统计] \x1b[0m数据处理完成:`);
  const level1Count = processedData.filter((d) => d.数据级别 === 1).length;
  const level2Count = processedData.filter((d) => d.数据级别 === 2).length;
  const level3Count = processedData.filter((d) => d.数据级别 === 3).length;

  console.log(`  \x1b[32m1级数据(分部): ${level1Count} 项\x1b[0m`);
  console.log(`  \x1b[32m2级数据(目标): ${level2Count} 项\x1b[0m`);
  console.log(`  \x1b[32m3级数据(人/材/专): ${level3Count} 项\x1b[0m`);
  console.log(`  \x1b[32m总计: ${processedData.length} 项\x1b[0m`);

  // 统计材料类型分布
  const 材料类型统计 = {};
  processedData
    .filter((d) => d.数据级别 === 3 && d.材料类型)
    .forEach((item) => {
      材料类型统计[item.材料类型] = (材料类型统计[item.材料类型] || 0) + 1;
    });

  if (Object.keys(材料类型统计).length > 0) {
    console.log(`\n\x1b[36m[材料类型] \x1b[0m3级数据材料类型分布:`);
    Object.entries(材料类型统计).forEach(([类型, 数量]) => {
      console.log(`  \x1b[33m${类型}: ${数量} 项\x1b[0m`);
    });
  }

  return processedData;
}

/**
 * 验证处理后的数据
 * @param {Array} processedData - 处理后的数据
 * @returns {boolean} 验证是否通过
 */
function validateProcessedData(processedData) {
  console.log(`\n\x1b[36m[验证] \x1b[0m开始验证数据完整性...`);

  let isValid = true;
  const errors = [];

  for (let i = 0; i < processedData.length; i++) {
    const item = processedData[i];

    // 验证必要字段
    if (!item.分部) {
      errors.push(`第${i + 1}项数据缺少"分部"字段`);
      isValid = false;
    }

    if (!item.数据级别 || ![1, 2, 3].includes(item.数据级别)) {
      errors.push(`第${i + 1}项数据的"数据级别"字段无效`);
      isValid = false;
    }

    // 验证2级和3级数据的父级关系
    if (item.数据级别 === 2 && !item.父级分部) {
      errors.push(`第${i + 1}项2级数据缺少"父级分部"字段`);
      isValid = false;
    }

    if (item.数据级别 === 3) {
      if (!item.父级分部) {
        errors.push(`第${i + 1}项3级数据缺少"父级分部"字段`);
        isValid = false;
      }
      if (!item.父级目标) {
        errors.push(`第${i + 1}项3级数据缺少"父级目标"字段`);
        isValid = false;
      }
    }
  }

  if (isValid) {
    console.log(`  \x1b[32m[通过] \x1b[0m数据验证通过`);
  } else {
    console.log(
      `  \x1b[31m[失败] \x1b[0m数据验证失败，发现 ${errors.length} 个错误:`
    );
    errors.forEach((error, index) => {
      console.log(`    ${index + 1}. ${error}`);
    });
  }

  return isValid;
}

module.exports = {
  processRawData,
  validateProcessedData,
};
