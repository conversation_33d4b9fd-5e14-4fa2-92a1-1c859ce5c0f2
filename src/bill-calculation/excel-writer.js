const ExcelJS = require("exceljs");
const path = require("path");

/**
 * 创建 3-分部分项成本测算 Excel工作簿
 * @returns {Object} 包含工作簿和工作表的对象
 */
function createCostCalculationWorkbook() {
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet("3-分部分项成本测算");

  // 设置表头
  worksheet.columns = [
    { header: "序号", key: "serialNo", width: 8 }, // A列
    { header: "分部", key: "division", width: 15 }, // B列
    { header: "目标", key: "target", width: 30 }, // C列
    { header: "项目特征", key: "feature", width: 40 }, // D列
    { header: "计量单位", key: "unit", width: 12 }, // E列
    { header: "工程量", key: "quantity", width: 15 }, // F列
    { header: "含量", key: "content", width: 12 }, // G列
    { header: "测算成本量", key: "costAmount", width: 15 }, // H列
    { header: "单价", key: "unitPrice", width: 12 }, // I列
    { header: "合价", key: "totalPrice", width: 15 }, // J列
    { header: "备注", key: "remark", width: 20 }, // K列
  ];

  // 设置表头样式
  const headerRow = worksheet.getRow(1);
  headerRow.font = { bold: true };
  headerRow.fill = {
    type: "pattern",
    pattern: "solid",
    fgColor: { argb: "FFE0E0E0" },
  };

  return { workbook, worksheet };
}

/**
 * 添加数据行到工作表
 * @param {Object} worksheet - Excel工作表对象
 * @param {Array} processedData - 处理后的数据数组
 */
function addDataRows(worksheet, processedData) {
  let rowIndex = 2; // 从第2行开始（第1行是表头）
  let parentRowIndex = null; // 记录父级行的行号

  processedData.forEach((item, index) => {
    // 添加数据行
    const row = worksheet.addRow({
      serialNo: item.序号,
      division: item.分部,
      target: item.目标,
      feature: item.项目特征,
      unit: item.计量单位,
      quantity: item.工程量,
      content: item.含量,
      costAmount: "", // H列暂时为空，后面添加公式
      unitPrice: "", // I列暂时为空
      totalPrice: "", // J列暂时为空
      remark: "", // K列暂时为空
    });

    // 根据数据级别处理公式
    if (item.数据级别 === 2) {
      // 2级数据（目标）- 记录为父级行
      parentRowIndex = rowIndex;
    } else if (["人", "材", "专"].includes(item.分部) && parentRowIndex) {
      // 3级数据（人、材、专）- 使用父级行的工程量 × 当前行的含量
      const parentFColumn = `F${parentRowIndex}`; // 父级行的工程量（F列）
      const currentGColumn = `G${rowIndex}`; // 当前行的含量（G列）
      const formula = `=${parentFColumn}*${currentGColumn}`;

      row.getCell(8).value = { formula: formula }; // H列

      console.log(
        `  \x1b[36m[公式] \x1b[0m第${rowIndex}行 H列: \x1b[33m${formula}\x1b[0m (${item.分部}: ${item.目标}) - 父级第${parentRowIndex}行`
      );
    }

    // 设置数据行样式
    setRowStyles(worksheet, rowIndex, item);

    rowIndex++;
  });

  return rowIndex - 1; // 返回总行数
}

/**
 * 设置行样式
 * @param {Object} worksheet - Excel工作表对象
 * @param {number} rowIndex - 行索引
 * @param {Object} item - 数据项
 */
function setRowStyles(worksheet, rowIndex, item) {
  const row = worksheet.getRow(rowIndex);

  // 根据数据级别设置不同样式
  switch (item.数据级别) {
    case 1: // 分部级别
      row.font = { bold: true };
      row.fill = {
        type: "pattern",
        pattern: "solid",
        fgColor: { argb: "FFD0D0D0" }, // 深灰色
      };
      break;
    case 2: // 目标级别
      row.font = { bold: false };
      row.fill = {
        type: "pattern",
        pattern: "solid",
        fgColor: { argb: "FFF0F0F0" }, // 浅灰色
      };
      break;
    case 3: // 人/材/专级别
      row.font = { bold: false };
      // 不设置背景色，保持默认白色
      break;
  }

  // 设置数字格式
  setNumberFormats(row, item);
}

/**
 * 设置数字格式
 * @param {Object} row - Excel行对象
 * @param {Object} item - 数据项
 */
function setNumberFormats(row, item) {
  // F列：工程量 - 数字格式
  if (item.工程量 && !isNaN(parseFloat(item.工程量))) {
    row.getCell(6).numFmt = "0.00";
  }

  // G列：含量 - 数字格式
  if (item.含量 && !isNaN(parseFloat(item.含量))) {
    row.getCell(7).numFmt = "0.00";
  }

  // H列：测算成本量 - 数字格式
  row.getCell(8).numFmt = "0.00";

  // I列：单价 - 数字格式
  row.getCell(9).numFmt = "0.00";

  // J列：合价 - 数字格式
  row.getCell(10).numFmt = "0.00";
}

/**
 * 设置单元格边框和对齐方式
 * @param {Object} worksheet - Excel工作表对象
 * @param {number} rowCount - 总行数
 */
function setCellStyles(worksheet, rowCount) {
  // 设置所有单元格的边框和对齐方式
  for (let i = 1; i <= rowCount; i++) {
    for (let j = 1; j <= 11; j++) {
      // A到K列
      const cell = worksheet.getCell(i, j);

      // 设置边框
      cell.border = {
        top: { style: "thin" },
        left: { style: "thin" },
        bottom: { style: "thin" },
        right: { style: "thin" },
      };

      // 设置对齐方式
      cell.alignment = {
        vertical: "middle",
        wrapText: true,
      };

      // 数字列居中对齐
      if (j >= 5 && j <= 10) {
        // E到J列（计量单位、工程量、含量、测算成本量、单价、合价）
        cell.alignment.horizontal = "center";
      }
    }
  }
}

/**
 * 生成 Excel 文件
 * @param {Array} processedData - 处理后的数据数组
 * @param {string} outputDir - 输出目录
 * @param {string} fileName - 文件名（不含扩展名）
 * @returns {Promise<string>} 生成的Excel文件路径
 */
async function generateCostCalculationExcel(
  processedData,
  outputDir,
  fileName
) {
  console.log(
    `\n\x1b[36m[生成] \x1b[0m开始生成 Excel 文件: \x1b[33m${fileName}.xlsx\x1b[0m`
  );

  // 创建工作簿和工作表
  const { workbook, worksheet } = createCostCalculationWorkbook();

  // 添加数据行
  const rowCount = addDataRows(worksheet, processedData);

  // 设置单元格样式
  setCellStyles(worksheet, rowCount);

  // 生成输出文件路径
  const outputFilePath = path.join(outputDir, `${fileName}.xlsx`);

  try {
    // 保存Excel文件
    await workbook.xlsx.writeFile(outputFilePath);

    console.log(
      `\x1b[32m[成功] \x1b[0m Excel文件已保存: \x1b[33m${outputFilePath}\x1b[0m`
    );
    console.log(
      `\x1b[36m[信息] \x1b[0m总行数: \x1b[32m${rowCount}\x1b[0m (包含表头)`
    );

    return outputFilePath;
  } catch (error) {
    console.error(`\x1b[31m[错误] \x1b[0m生成Excel文件失败: ${error.message}`);
    throw error;
  }
}

module.exports = {
  createCostCalculationWorkbook,
  addDataRows,
  setCellStyles,
  generateCostCalculationExcel,
};
