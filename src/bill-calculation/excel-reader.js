const ExcelJS = require("exceljs");
const fs = require("fs");
const path = require("path");

/**
 * 读取Excel文件并返回工作簿对象
 * @param {string} filePath - Excel文件路径
 * @returns {Promise<Object>} Excel工作簿对象
 */
async function readExcelFile(filePath) {
  const workbook = new ExcelJS.Workbook();
  await workbook.xlsx.readFile(filePath);
  return workbook;
}

/**
 * 获取input目录下的所有Excel文件
 * @param {string} inputDir - input目录路径
 * @returns {Array<string>} Excel文件名数组
 */
function getExcelFiles(inputDir) {
  return fs
    .readdirSync(inputDir)
    .filter((file) => file.endsWith(".xlsx") || file.endsWith(".xls"));
}

/**
 * 获取单元格的值（优先获取计算结果，而非公式）
 * @param {Object} cell - Excel单元格对象
 * @returns {string} 单元格的值
 */
function getCellValue(cell) {
  if (!cell || cell.value === null || cell.value === undefined) {
    return "";
  }

  const value = cell.value;

  // 处理公式单元格 - 优先返回计算结果
  if (typeof value === "object" && value.formula) {
    // 如果有计算结果，返回结果值
    if (value.result !== null && value.result !== undefined) {
      return value.result.toString().trim();
    }
    // 如果没有计算结果，返回公式（作为备选）
    return value.formula.toString().trim();
  }

  // 处理富文本格式
  if (typeof value === "object" && value.richText) {
    return value.richText.map((rt) => rt.text).join("");
  }

  // 处理其他类型的值
  return value.toString().trim();
}

/**
 * 读取 "3-分部分项成本测算" 工作表的 A~G 列数据
 * @param {string} filePath - Excel文件路径
 * @returns {Promise<Array>} 原始数据数组
 */
async function readCostCalculationData(filePath) {
  console.log(
    `\n\x1b[36m[读取] \x1b[0m文件: \x1b[33m${path.basename(filePath)}\x1b[0m`
  );

  const workbook = await readExcelFile(filePath);

  // 查找 "3-分部分项成本测算" 工作表
  const worksheet = workbook.getWorksheet("3-分部分项成本测算");
  if (!worksheet) {
    throw new Error('未找到 "3-分部分项成本测算" 工作表');
  }

  console.log(
    `\x1b[36m[信息] \x1b[0m工作表行数: \x1b[32m${worksheet.rowCount}\x1b[0m`
  );

  const rawData = [];

  // 从第4行开始读取数据（跳过表头）
  for (let rowNum = 4; rowNum <= worksheet.rowCount; rowNum++) {
    const row = worksheet.getRow(rowNum);

    // 检查是否为空行
    let isEmpty = true;
    for (let col = 1; col <= 7; col++) {
      const cellValue = getCellValue(row.getCell(col));
      if (cellValue) {
        isEmpty = false;
        break;
      }
    }

    if (isEmpty) continue;

    // 读取 A~G 列数据
    const rowData = {
      序号: getCellValue(row.getCell(1)), // A列
      分部: getCellValue(row.getCell(2)), // B列
      目标: getCellValue(row.getCell(3)), // C列
      项目特征: getCellValue(row.getCell(4)), // D列
      计量单位: getCellValue(row.getCell(5)), // E列
      工程量: getCellValue(row.getCell(6)), // F列
      含量: getCellValue(row.getCell(7)), // G列
      原始行号: rowNum,
    };

    rawData.push(rowData);
  }

  console.log(
    `\x1b[36m[完成] \x1b[0m读取到 \x1b[32m${rawData.length}\x1b[0m 行数据`
  );

  return rawData;
}

module.exports = {
  readExcelFile,
  getExcelFiles,
  getCellValue,
  readCostCalculationData,
};
